using System;
using System.Collections.Generic;
using UnityEngine;

namespace WastelandReclaim
{
    // Enums
    public enum ResourceType
    {
        Metal,
        Plastic,
        Wood,
        Electronic,
        Glass,
        Organic,
        CoreFragment,
        Core
    }

    public enum WasteBlockType
    {
        Metal,
        Plastic,
        Wood,
        Electronic,
        Glass,
        Organic,
        PollutionCore,
        RegenerationCore,
        MutatedWaste,
        FrozenWaste,
        ExplosiveWaste
    }

    public enum EnemyType
    {
        PollutionCrawler,
        CorrosiveFly,
        MutatedPlant,
        PollutionKing
    }

    public enum TowerType
    {
        WindFilter,
        SonicRepeller,
        ShieldGenerator,
        ResourceAccelerator,
        LaserTurret,
        ElectromagneticNet
    }

    public enum BuildingType
    {
        CommandCenter,
        ResourceProcessor,
        ResearchLab,
        ResidentialArea,
        PowerPlant,
        WasteRecycler
    }

    public enum TechnologyType
    {
        MatchEfficiency,
        ChainReaction,
        TimeControl,
        TowerEfficiency,
        BuildingSpeed,
        AutoRepair
    }

    public enum SpecialEffectType
    {
        ChainPurification,
        ElementalReaction,
        TimeFreeze,
        MassDestruction,
        Regeneration
    }

    // Data Models
    [Serializable]
    public class LevelData
    {
        public int levelId;
        public string levelName;
        public string chapterName;
        public int difficulty;
        public Vector2Int gridSize;
        public int initialPollution;
        public int targetPollution;
        public int stepLimit;
        public float timeLimit;
        public List<WasteBlockData> wasteBlocks;
        public List<EnemyWaveData> enemyWaves;
        public Dictionary<ResourceType, int> basicReward;
        public List<StarReward> starRewards;
        public List<Vector2Int> blockedPositions;
        public Vector2Int purifierPosition;

        public LevelData()
        {
            wasteBlocks = new List<WasteBlockData>();
            enemyWaves = new List<EnemyWaveData>();
            basicReward = new Dictionary<ResourceType, int>();
            starRewards = new List<StarReward>();
            blockedPositions = new List<Vector2Int>();
        }
    }

    [Serializable]
    public class WasteBlockData
    {
        public WasteBlockType type;
        public Vector2Int position;
        public int pollutionValue;
        public Dictionary<string, object> properties;

        public WasteBlockData()
        {
            properties = new Dictionary<string, object>();
        }
    }

    [Serializable]
    public class EnemyWaveData
    {
        public float spawnTime;
        public EnemyType enemyType;
        public int count;
        public List<Vector2Int> spawnPositions;
        public List<Vector2Int> path;
        public float spawnInterval;

        public EnemyWaveData()
        {
            spawnPositions = new List<Vector2Int>();
            path = new List<Vector2Int>();
        }
    }

    [Serializable]
    public class StarReward
    {
        public int stars;
        public string condition;
        public Dictionary<ResourceType, int> reward;

        public StarReward()
        {
            reward = new Dictionary<ResourceType, int>();
        }
    }

    [Serializable]
    public class PlayerData
    {
        public string playerId;
        public string playerName;
        public int level;
        public int experience;
        public Dictionary<ResourceType, int> resources;
        public List<int> completedLevels;
        public Dictionary<int, int> levelStars;
        public BaseData baseData;
        public Dictionary<TechnologyType, int> technologies;
        public SettingsData settings;
        public DateTime lastPlayTime;
        public int totalPlayTime;

        public PlayerData()
        {
            playerId = System.Guid.NewGuid().ToString();
            playerName = "Player";
            level = 1;
            experience = 0;
            resources = new Dictionary<ResourceType, int>();
            completedLevels = new List<int>();
            levelStars = new Dictionary<int, int>();
            baseData = new BaseData();
            technologies = new Dictionary<TechnologyType, int>();
            settings = new SettingsData();
            lastPlayTime = DateTime.Now;
            totalPlayTime = 0;

            // Initialize resources
            foreach (ResourceType type in Enum.GetValues(typeof(ResourceType)))
            {
                resources[type] = 0;
            }

            // Starting resources
            resources[ResourceType.Metal] = 50;
            resources[ResourceType.Plastic] = 30;
            resources[ResourceType.Core] = 5;
        }

        public void AddResource(ResourceType type, int amount)
        {
            resources[type] += amount;
        }

        public bool CanAfford(Dictionary<ResourceType, int> cost)
        {
            foreach (var kvp in cost)
            {
                if (resources[kvp.Key] < kvp.Value)
                    return false;
            }
            return true;
        }

        public void SpendResources(Dictionary<ResourceType, int> cost)
        {
            foreach (var kvp in cost)
            {
                resources[kvp.Key] -= kvp.Value;
            }
        }

        public void AddExperience(int amount)
        {
            experience += amount;
            CheckLevelUp();
        }

        private void CheckLevelUp()
        {
            int requiredExp = GetRequiredExperience(level + 1);
            if (experience >= requiredExp)
            {
                level++;
                EventSystem.Publish(new ResourceChangedEvent());
            }
        }

        private int GetRequiredExperience(int targetLevel)
        {
            return targetLevel * 100 + (targetLevel - 1) * 50;
        }
    }

    [Serializable]
    public class BaseData
    {
        public Dictionary<BuildingType, BuildingInfo> buildings;
        public int populationCapacity;
        public int currentPopulation;
        public Vector2Int baseSize;

        public BaseData()
        {
            buildings = new Dictionary<BuildingType, BuildingInfo>();
            populationCapacity = 10;
            currentPopulation = 5;
            baseSize = new Vector2Int(10, 10);

            // Initialize with command center
            buildings[BuildingType.CommandCenter] = new BuildingInfo
            {
                buildingType = BuildingType.CommandCenter,
                level = 1,
                position = new Vector2Int(5, 5),
                isConstructed = true
            };
        }
    }

    [Serializable]
    public class BuildingInfo
    {
        public BuildingType buildingType;
        public int level;
        public Vector2Int position;
        public bool isConstructed;
        public DateTime constructionStartTime;
        public float constructionDuration;

        public bool IsUnderConstruction()
        {
            return !isConstructed && DateTime.Now < constructionStartTime.AddSeconds(constructionDuration);
        }

        public float GetConstructionProgress()
        {
            if (isConstructed) return 1f;
            
            float elapsed = (float)(DateTime.Now - constructionStartTime).TotalSeconds;
            return Mathf.Clamp01(elapsed / constructionDuration);
        }
    }

    [Serializable]
    public class SettingsData
    {
        public bool soundEnabled = true;
        public bool musicEnabled = true;
        public float soundVolume = 1f;
        public float musicVolume = 0.7f;
        public string language = "zh-CN";
        public bool notificationsEnabled = true;
        public int graphicsQuality = 2; // 0=Low, 1=Medium, 2=High
        public bool vibrationEnabled = true;
    }

    [Serializable]
    public class TowerData
    {
        public TowerType towerType;
        public int level;
        public Vector2Int position;
        public Dictionary<ResourceType, int> upgradeCost;
        public TowerStats stats;

        public TowerData()
        {
            upgradeCost = new Dictionary<ResourceType, int>();
            stats = new TowerStats();
        }
    }

    [Serializable]
    public class TowerStats
    {
        public float range;
        public float damage;
        public float attackSpeed;
        public float effectDuration;
        public int effectRadius;
        public Dictionary<string, float> specialEffects;

        public TowerStats()
        {
            specialEffects = new Dictionary<string, float>();
        }
    }

    [Serializable]
    public class EnemyData
    {
        public EnemyType enemyType;
        public int health;
        public float moveSpeed;
        public int pollutionDamage;
        public List<Vector2Int> path;
        public Dictionary<string, object> specialProperties;

        public EnemyData()
        {
            path = new List<Vector2Int>();
            specialProperties = new Dictionary<string, object>();
        }
    }

    // Configuration data for game balance
    [Serializable]
    public class GameConfig
    {
        public Dictionary<WasteBlockType, ResourceDropData> wasteBlockDrops;
        public Dictionary<TowerType, TowerConfigData> towerConfigs;
        public Dictionary<EnemyType, EnemyConfigData> enemyConfigs;
        public Dictionary<BuildingType, BuildingConfigData> buildingConfigs;
        public Dictionary<TechnologyType, TechnologyConfigData> technologyConfigs;

        public GameConfig()
        {
            wasteBlockDrops = new Dictionary<WasteBlockType, ResourceDropData>();
            towerConfigs = new Dictionary<TowerType, TowerConfigData>();
            enemyConfigs = new Dictionary<EnemyType, EnemyConfigData>();
            buildingConfigs = new Dictionary<BuildingType, BuildingConfigData>();
            technologyConfigs = new Dictionary<TechnologyType, TechnologyConfigData>();
            
            InitializeDefaultConfig();
        }

        private void InitializeDefaultConfig()
        {
            // Initialize waste block drops
            wasteBlockDrops[WasteBlockType.Metal] = new ResourceDropData
            {
                primaryResource = ResourceType.Metal,
                minAmount = 2,
                maxAmount = 4,
                pollutionReduction = 2
            };

            wasteBlockDrops[WasteBlockType.Plastic] = new ResourceDropData
            {
                primaryResource = ResourceType.Plastic,
                minAmount = 1,
                maxAmount = 3,
                pollutionReduction = 2
            };

            // Initialize tower configs
            towerConfigs[TowerType.WindFilter] = new TowerConfigData
            {
                cost = new Dictionary<ResourceType, int>
                {
                    { ResourceType.Metal, 3 },
                    { ResourceType.Plastic, 2 },
                    { ResourceType.Core, 1 }
                },
                baseStats = new TowerStats
                {
                    range = 3f,
                    damage = 0f,
                    attackSpeed = 1f,
                    effectDuration = 1f,
                    effectRadius = 3
                }
            };

            // Initialize enemy configs
            enemyConfigs[EnemyType.PollutionCrawler] = new EnemyConfigData
            {
                baseHealth = 3,
                baseMoveSpeed = 1f,
                pollutionDamage = 10,
                spawnWeight = 70
            };
        }
    }

    [Serializable]
    public class ResourceDropData
    {
        public ResourceType primaryResource;
        public int minAmount;
        public int maxAmount;
        public int pollutionReduction;
        public float coreFragmentChance = 0.1f;
        public float coreChance = 0.01f;
    }

    [Serializable]
    public class TowerConfigData
    {
        public Dictionary<ResourceType, int> cost;
        public TowerStats baseStats;
        public List<TowerStats> upgradeLevels;

        public TowerConfigData()
        {
            cost = new Dictionary<ResourceType, int>();
            upgradeLevels = new List<TowerStats>();
        }
    }

    [Serializable]
    public class EnemyConfigData
    {
        public int baseHealth;
        public float baseMoveSpeed;
        public int pollutionDamage;
        public int spawnWeight;
        public Dictionary<string, object> specialAbilities;

        public EnemyConfigData()
        {
            specialAbilities = new Dictionary<string, object>();
        }
    }

    [Serializable]
    public class BuildingConfigData
    {
        public Dictionary<ResourceType, int> baseCost;
        public float baseConstructionTime;
        public List<Dictionary<ResourceType, int>> upgradeCosts;
        public List<float> upgradeConstructionTimes;

        public BuildingConfigData()
        {
            baseCost = new Dictionary<ResourceType, int>();
            upgradeCosts = new List<Dictionary<ResourceType, int>>();
            upgradeConstructionTimes = new List<float>();
        }
    }

    [Serializable]
    public class TechnologyConfigData
    {
        public Dictionary<ResourceType, int> researchCost;
        public float researchTime;
        public string description;
        public Dictionary<string, float> effects;

        public TechnologyConfigData()
        {
            researchCost = new Dictionary<ResourceType, int>();
            effects = new Dictionary<string, float>();
        }
    }
}
