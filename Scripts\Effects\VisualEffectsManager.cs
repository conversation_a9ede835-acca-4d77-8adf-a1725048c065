using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace WastelandReclaim
{
    /// <summary>
    /// 视觉效果管理器 - 创建高品质的游戏特效
    /// </summary>
    public class VisualEffectsManager : MonoBehaviour
    {
        [Header("粒子效果")]
        public ParticleSystem matchParticles;
        public ParticleSystem explosionParticles;
        public ParticleSystem comboParticles;
        public ParticleSystem backgroundParticles;

        [Header("材质和着色器")]
        public Material glowMaterial;
        public Material hologramMaterial;
        public Shader dissolveShader;

        [Header("后处理效果")]
        public bool enableScreenShake = true;
        public bool enableColorGrading = true;
        public bool enableBloom = true;

        private Camera mainCamera;
        private Dictionary<string, ParticleSystem> effectPool = new Dictionary<string, ParticleSystem>();

        private void Start()
        {
            mainCamera = Camera.main;
            InitializeEffects();
            SetupEventListeners();
        }

        private void InitializeEffects()
        {
            CreateMatchParticles();
            CreateExplosionParticles();
            CreateComboParticles();
            CreateBackgroundParticles();
            SetupCameraEffects();
        }

        private void CreateMatchParticles()
        {
            if (matchParticles == null)
            {
                GameObject particleObj = new GameObject("MatchParticles");
                matchParticles = particleObj.AddComponent<ParticleSystem>();
                
                var main = matchParticles.main;
                main.startLifetime = 1.0f;
                main.startSpeed = 5.0f;
                main.startSize = 0.2f;
                main.startColor = Color.yellow;
                main.maxParticles = 50;

                var emission = matchParticles.emission;
                emission.rateOverTime = 0;
                emission.SetBursts(new ParticleSystem.Burst[]
                {
                    new ParticleSystem.Burst(0.0f, 20)
                });

                var shape = matchParticles.shape;
                shape.shapeType = ParticleSystemShapeType.Circle;
                shape.radius = 0.5f;

                var velocityOverLifetime = matchParticles.velocityOverLifetime;
                velocityOverLifetime.enabled = true;
                velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
                velocityOverLifetime.radial = new ParticleSystem.MinMaxCurve(2f);

                var colorOverLifetime = matchParticles.colorOverLifetime;
                colorOverLifetime.enabled = true;
                Gradient gradient = new Gradient();
                gradient.SetKeys(
                    new GradientColorKey[] { new GradientColorKey(Color.yellow, 0.0f), new GradientColorKey(Color.red, 1.0f) },
                    new GradientAlphaKey[] { new GradientAlphaKey(1.0f, 0.0f), new GradientAlphaKey(0.0f, 1.0f) }
                );
                colorOverLifetime.color = gradient;
            }
        }

        private void CreateExplosionParticles()
        {
            if (explosionParticles == null)
            {
                GameObject particleObj = new GameObject("ExplosionParticles");
                explosionParticles = particleObj.AddComponent<ParticleSystem>();
                
                var main = explosionParticles.main;
                main.startLifetime = 2.0f;
                main.startSpeed = 8.0f;
                main.startSize = 0.3f;
                main.startColor = Color.orange;
                main.maxParticles = 100;

                var emission = explosionParticles.emission;
                emission.rateOverTime = 0;
                emission.SetBursts(new ParticleSystem.Burst[]
                {
                    new ParticleSystem.Burst(0.0f, 50)
                });

                var shape = explosionParticles.shape;
                shape.shapeType = ParticleSystemShapeType.Sphere;
                shape.radius = 1.0f;

                var velocityOverLifetime = explosionParticles.velocityOverLifetime;
                velocityOverLifetime.enabled = true;
                velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
                velocityOverLifetime.radial = new ParticleSystem.MinMaxCurve(5f);

                var sizeOverLifetime = explosionParticles.sizeOverLifetime;
                sizeOverLifetime.enabled = true;
                AnimationCurve sizeCurve = new AnimationCurve();
                sizeCurve.AddKey(0f, 0.5f);
                sizeCurve.AddKey(0.3f, 1.5f);
                sizeCurve.AddKey(1f, 0f);
                sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1f, sizeCurve);
            }
        }

        private void CreateComboParticles()
        {
            if (comboParticles == null)
            {
                GameObject particleObj = new GameObject("ComboParticles");
                comboParticles = particleObj.AddComponent<ParticleSystem>();
                
                var main = comboParticles.main;
                main.startLifetime = 1.5f;
                main.startSpeed = 3.0f;
                main.startSize = 0.4f;
                main.startColor = Color.cyan;
                main.maxParticles = 30;

                var emission = comboParticles.emission;
                emission.rateOverTime = 0;

                var shape = comboParticles.shape;
                shape.shapeType = ParticleSystemShapeType.Cone;
                shape.angle = 45f;

                var velocityOverLifetime = comboParticles.velocityOverLifetime;
                velocityOverLifetime.enabled = true;
                velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
                velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(2f);
            }
        }

        private void CreateBackgroundParticles()
        {
            if (backgroundParticles == null)
            {
                GameObject particleObj = new GameObject("BackgroundParticles");
                backgroundParticles = particleObj.AddComponent<ParticleSystem>();
                
                var main = backgroundParticles.main;
                main.startLifetime = 10.0f;
                main.startSpeed = 0.5f;
                main.startSize = 0.1f;
                main.startColor = new Color(1f, 1f, 1f, 0.3f);
                main.maxParticles = 200;
                main.loop = true;

                var emission = backgroundParticles.emission;
                emission.rateOverTime = 20;

                var shape = backgroundParticles.shape;
                shape.shapeType = ParticleSystemShapeType.Box;
                shape.scale = new Vector3(20, 15, 5);

                var velocityOverLifetime = backgroundParticles.velocityOverLifetime;
                velocityOverLifetime.enabled = true;
                velocityOverLifetime.space = ParticleSystemSimulationSpace.World;
                velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(-0.5f);

                // 设置粒子位置在相机后方
                particleObj.transform.position = new Vector3(0, 5, 10);
            }
        }

        private void SetupCameraEffects()
        {
            if (mainCamera == null) return;

            // 添加基础的后处理效果
            if (enableBloom)
            {
                // 这里可以添加Bloom效果的设置
                // 需要Universal Render Pipeline或类似的渲染管线
            }

            // 设置相机的基础渲染设置
            mainCamera.backgroundColor = new Color(0.1f, 0.2f, 0.4f);
            mainCamera.clearFlags = CameraClearFlags.SolidColor;
        }

        private void SetupEventListeners()
        {
            EventSystem.Subscribe<MatchEvent>(OnMatch);
            EventSystem.Subscribe<ComboEvent>(OnCombo);
            EventSystem.Subscribe<ScoreEvent>(OnScore);
        }

        private void OnMatch(MatchEvent matchEvent)
        {
            Vector3 worldPos = Camera.main.ScreenToWorldPoint(new Vector3(
                matchEvent.position.x, matchEvent.position.y, 10f));
            
            PlayMatchEffect(worldPos, matchEvent.matchedBlocks.Count);
            
            if (enableScreenShake)
            {
                StartCoroutine(ScreenShake(0.1f, 0.2f));
            }
        }

        private void OnCombo(ComboEvent comboEvent)
        {
            PlayComboEffect(comboEvent.comboCount);
            
            if (enableScreenShake && comboEvent.comboCount > 2)
            {
                StartCoroutine(ScreenShake(0.2f, 0.3f));
            }
        }

        private void OnScore(ScoreEvent scoreEvent)
        {
            CreateFloatingText(scoreEvent.points.ToString(), scoreEvent.position);
        }

        public void PlayMatchEffect(Vector3 position, int matchCount)
        {
            if (matchParticles != null)
            {
                matchParticles.transform.position = position;
                
                var emission = matchParticles.emission;
                emission.SetBursts(new ParticleSystem.Burst[]
                {
                    new ParticleSystem.Burst(0.0f, (short)(10 + matchCount * 5))
                });
                
                matchParticles.Play();
            }
        }

        public void PlayExplosionEffect(Vector3 position)
        {
            if (explosionParticles != null)
            {
                explosionParticles.transform.position = position;
                explosionParticles.Play();
            }
        }

        public void PlayComboEffect(int comboCount)
        {
            if (comboParticles != null && mainCamera != null)
            {
                Vector3 screenCenter = mainCamera.ScreenToWorldPoint(new Vector3(
                    Screen.width * 0.5f, Screen.height * 0.7f, 10f));
                
                comboParticles.transform.position = screenCenter;
                
                var emission = comboParticles.emission;
                emission.SetBursts(new ParticleSystem.Burst[]
                {
                    new ParticleSystem.Burst(0.0f, (short)(comboCount * 10))
                });
                
                comboParticles.Play();
            }
        }

        private void CreateFloatingText(string text, Vector3 worldPosition)
        {
            StartCoroutine(FloatingTextCoroutine(text, worldPosition));
        }

        private IEnumerator FloatingTextCoroutine(string text, Vector3 worldPosition)
        {
            GameObject textObj = new GameObject("FloatingText");
            textObj.transform.position = worldPosition;

            TextMesh textMesh = textObj.AddComponent<TextMesh>();
            textMesh.text = text;
            textMesh.fontSize = 20;
            textMesh.color = Color.yellow;
            textMesh.anchor = TextAnchor.MiddleCenter;

            // 添加发光效果
            if (glowMaterial != null)
            {
                textMesh.GetComponent<Renderer>().material = glowMaterial;
            }

            Vector3 startPos = worldPosition;
            Vector3 endPos = startPos + Vector3.up * 2f;
            Color startColor = textMesh.color;
            Color endColor = new Color(startColor.r, startColor.g, startColor.b, 0f);

            float duration = 1.5f;
            float elapsed = 0f;

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / duration;

                textObj.transform.position = Vector3.Lerp(startPos, endPos, t);
                textMesh.color = Color.Lerp(startColor, endColor, t);

                // 添加轻微的摆动
                Vector3 wobble = new Vector3(
                    Mathf.Sin(t * Mathf.PI * 4) * 0.1f,
                    0,
                    0
                );
                textObj.transform.position += wobble;

                yield return null;
            }

            Destroy(textObj);
        }

        private IEnumerator ScreenShake(float duration, float magnitude)
        {
            if (mainCamera == null) yield break;

            Vector3 originalPosition = mainCamera.transform.position;
            float elapsed = 0f;

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;

                float x = Random.Range(-1f, 1f) * magnitude;
                float y = Random.Range(-1f, 1f) * magnitude;

                mainCamera.transform.position = originalPosition + new Vector3(x, y, 0);

                yield return null;
            }

            mainCamera.transform.position = originalPosition;
        }

        public void CreateBlockGlowEffect(GameObject block)
        {
            if (glowMaterial == null) return;

            Renderer renderer = block.GetComponent<Renderer>();
            if (renderer != null)
            {
                Material[] materials = renderer.materials;
                System.Array.Resize(ref materials, materials.Length + 1);
                materials[materials.Length - 1] = glowMaterial;
                renderer.materials = materials;
            }
        }

        public void CreateHologramEffect(GameObject target)
        {
            if (hologramMaterial == null) return;

            StartCoroutine(HologramEffectCoroutine(target));
        }

        private IEnumerator HologramEffectCoroutine(GameObject target)
        {
            Renderer renderer = target.GetComponent<Renderer>();
            if (renderer == null) yield break;

            Material originalMaterial = renderer.material;
            renderer.material = hologramMaterial;

            float duration = 2f;
            float elapsed = 0f;

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float alpha = Mathf.PingPong(elapsed * 2f, 1f);
                
                Color color = hologramMaterial.color;
                color.a = alpha * 0.7f;
                hologramMaterial.color = color;

                yield return null;
            }

            renderer.material = originalMaterial;
        }

        public void CreateDissolveEffect(GameObject target, System.Action onComplete = null)
        {
            StartCoroutine(DissolveEffectCoroutine(target, onComplete));
        }

        private IEnumerator DissolveEffectCoroutine(GameObject target, System.Action onComplete)
        {
            Renderer renderer = target.GetComponent<Renderer>();
            if (renderer == null || dissolveShader == null)
            {
                onComplete?.Invoke();
                yield break;
            }

            Material dissolveMaterial = new Material(dissolveShader);
            dissolveMaterial.mainTexture = renderer.material.mainTexture;
            renderer.material = dissolveMaterial;

            float duration = 1f;
            float elapsed = 0f;

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float dissolveAmount = elapsed / duration;
                
                dissolveMaterial.SetFloat("_DissolveAmount", dissolveAmount);

                yield return null;
            }

            onComplete?.Invoke();
        }

        public void SetBackgroundParticleIntensity(float intensity)
        {
            if (backgroundParticles != null)
            {
                var emission = backgroundParticles.emission;
                emission.rateOverTime = intensity * 20f;
            }
        }

        private void OnDestroy()
        {
            EventSystem.Unsubscribe<MatchEvent>(OnMatch);
            EventSystem.Unsubscribe<ComboEvent>(OnCombo);
            EventSystem.Unsubscribe<ScoreEvent>(OnScore);
        }
    }
}
