using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace WastelandReclaim
{
    public class MatchSystem : MonoBehaviour
    {
        [Header("Match Configuration")]
        public int minMatchCount = 3;
        public float matchDelay = 0.5f;
        public float comboTimeLimit = 2f;

        [Header("Effects")]
        public ParticleSystem matchEffect;
        public ParticleSystem comboEffect;
        public AudioClip matchSound;
        public AudioClip comboSound;

        private int currentCombo = 0;
        private float comboTimer = 0f;
        private AudioSource audioSource;
        private GridSystem gridSystem;

        public void Initialize()
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }

            gridSystem = FindObjectOfType<GridSystem>();
            
            // Subscribe to events
            EventSystem.Subscribe<MatchEvent>(OnMatchProcessed);
        }

        private void Update()
        {
            UpdateComboTimer();
        }

        private void UpdateComboTimer()
        {
            if (currentCombo > 0)
            {
                comboTimer -= Time.deltaTime;
                if (comboTimer <= 0f)
                {
                    ResetCombo();
                }
            }
        }

        public void ProcessMatch(List<Vector2Int> matchedBlocks, WasteBlockType blockType)
        {
            if (matchedBlocks.Count < minMatchCount)
                return;

            StartCoroutine(ProcessMatchCoroutine(matchedBlocks, blockType));
        }

        private IEnumerator ProcessMatchCoroutine(List<Vector2Int> matchedBlocks, WasteBlockType blockType)
        {
            // Update combo
            UpdateCombo();

            // Calculate multiplier
            float multiplier = CalculateMultiplier(matchedBlocks.Count);

            // Play match effect
            PlayMatchEffect(matchedBlocks);

            // Calculate resources
            var resources = CalculateResources(matchedBlocks, blockType, multiplier);

            // Wait for effect
            yield return new WaitForSeconds(matchDelay);

            // Create and publish match event
            var matchEvent = new MatchEvent
            {
                matchedBlocks = matchedBlocks,
                resources = resources,
                combo = currentCombo,
                blockType = blockType
            };

            EventSystem.Publish(matchEvent);

            // Check for special effects
            CheckSpecialEffects(matchedBlocks, blockType);

            // Play combo effect if applicable
            if (currentCombo > 1)
            {
                PlayComboEffect(matchedBlocks);
            }
        }

        private void UpdateCombo()
        {
            currentCombo++;
            comboTimer = comboTimeLimit;

            // Publish combo event
            EventSystem.Publish(new ComboEvent
            {
                comboCount = currentCombo,
                multiplier = GetComboMultiplier()
            });
        }

        private void ResetCombo()
        {
            currentCombo = 0;
            comboTimer = 0f;
        }

        private float CalculateMultiplier(int matchCount)
        {
            float baseMultiplier = 1f;

            // Match count bonus
            if (matchCount >= 6) baseMultiplier *= 3f;
            else if (matchCount >= 5) baseMultiplier *= 2f;
            else if (matchCount >= 4) baseMultiplier *= 1.5f;

            // Combo bonus
            baseMultiplier *= GetComboMultiplier();

            return baseMultiplier;
        }

        private float GetComboMultiplier()
        {
            return 1f + (currentCombo - 1) * 0.1f;
        }

        private Dictionary<ResourceType, int> CalculateResources(List<Vector2Int> matchedBlocks, WasteBlockType blockType, float multiplier)
        {
            var resources = new Dictionary<ResourceType, int>();

            foreach (var position in matchedBlocks)
            {
                var wasteBlock = gridSystem.GetWasteBlock(position);
                if (wasteBlock != null)
                {
                    var blockResources = wasteBlock.GetResources();
                    foreach (var kvp in blockResources)
                    {
                        if (!resources.ContainsKey(kvp.Key))
                            resources[kvp.Key] = 0;
                        resources[kvp.Key] += kvp.Value;
                    }
                }
            }

            // Apply multiplier
            foreach (var key in resources.Keys.ToList())
            {
                resources[key] = Mathf.RoundToInt(resources[key] * multiplier);
            }

            // Bonus resources for special matches
            AddBonusResources(resources, matchedBlocks, blockType);

            return resources;
        }

        private void AddBonusResources(Dictionary<ResourceType, int> resources, List<Vector2Int> matchedBlocks, WasteBlockType blockType)
        {
            // Special shape bonuses
            if (IsLShape(matchedBlocks))
            {
                if (!resources.ContainsKey(ResourceType.CoreFragment))
                    resources[ResourceType.CoreFragment] = 0;
                resources[ResourceType.CoreFragment] += 1;
            }
            else if (IsTShape(matchedBlocks))
            {
                if (!resources.ContainsKey(ResourceType.CoreFragment))
                    resources[ResourceType.CoreFragment] = 0;
                resources[ResourceType.CoreFragment] += 2;
            }
            else if (IsCrossShape(matchedBlocks))
            {
                if (!resources.ContainsKey(ResourceType.Core))
                    resources[ResourceType.Core] = 0;
                resources[ResourceType.Core] += 1;
            }

            // Large match bonus
            if (matchedBlocks.Count >= 8)
            {
                if (!resources.ContainsKey(ResourceType.Core))
                    resources[ResourceType.Core] = 0;
                resources[ResourceType.Core] += 1;
            }

            // Combo bonus resources
            if (currentCombo >= 5)
            {
                foreach (var key in resources.Keys.ToList())
                {
                    resources[key] += 1;
                }
            }
        }

        private bool IsLShape(List<Vector2Int> blocks)
        {
            if (blocks.Count < 5) return false;

            // Check for L-shape pattern
            // This is a simplified check - a full implementation would be more robust
            var sortedBlocks = new List<Vector2Int>(blocks);
            sortedBlocks.Sort((a, b) => a.x != b.x ? a.x.CompareTo(b.x) : a.y.CompareTo(b.y));

            // Check if blocks form an L shape
            // Implementation would check for corner patterns
            return false; // Simplified for now
        }

        private bool IsTShape(List<Vector2Int> blocks)
        {
            if (blocks.Count < 5) return false;
            // Similar implementation to L-shape but for T pattern
            return false; // Simplified for now
        }

        private bool IsCrossShape(List<Vector2Int> blocks)
        {
            if (blocks.Count < 5) return false;
            // Check for cross/plus pattern
            return false; // Simplified for now
        }

        private void CheckSpecialEffects(List<Vector2Int> matchedBlocks, WasteBlockType blockType)
        {
            // Check for elemental reactions
            CheckElementalReactions(matchedBlocks);

            // Check for chain reactions
            CheckChainReactions(matchedBlocks);

            // Special block effects
            foreach (var position in matchedBlocks)
            {
                var wasteBlock = gridSystem.GetWasteBlock(position);
                if (wasteBlock != null && wasteBlock.IsSpecialBlock())
                {
                    TriggerSpecialBlockEffect(wasteBlock, position);
                }
            }
        }

        private void CheckElementalReactions(List<Vector2Int> matchedBlocks)
        {
            // Check for adjacent different block types that can react
            var adjacentTypes = new HashSet<WasteBlockType>();

            foreach (var position in matchedBlocks)
            {
                var neighbors = GameUtils.GetNeighbors(position, gridSystem.gridWidth, gridSystem.gridHeight);
                foreach (var neighbor in neighbors)
                {
                    var neighborBlock = gridSystem.GetWasteBlock(neighbor);
                    if (neighborBlock != null && !matchedBlocks.Contains(neighbor))
                    {
                        adjacentTypes.Add(neighborBlock.blockType);
                    }
                }
            }

            // Trigger reactions based on adjacent types
            foreach (var type in adjacentTypes)
            {
                TriggerElementalReaction(matchedBlocks[0], type);
            }
        }

        private void TriggerElementalReaction(Vector2Int position, WasteBlockType reactingType)
        {
            // Different reactions based on block types
            switch (reactingType)
            {
                case WasteBlockType.Metal:
                    // Metal + Electronic = Electromagnetic pulse
                    TriggerElectromagneticPulse(position);
                    break;
                case WasteBlockType.Organic:
                    // Organic + Glass = Greenhouse effect
                    TriggerGreenhouseEffect(position);
                    break;
                case WasteBlockType.Electronic:
                    // Electronic reactions
                    TriggerElectronicReaction(position);
                    break;
            }
        }

        private void TriggerElectromagneticPulse(Vector2Int center)
        {
            var affectedPositions = GameUtils.GetPositionsInRange(center, 2, gridSystem.gridWidth, gridSystem.gridHeight);
            
            EventSystem.Publish(new SpecialEffectEvent
            {
                effectType = SpecialEffectType.ElementalReaction,
                position = center,
                affectedPositions = affectedPositions
            });
        }

        private void TriggerGreenhouseEffect(Vector2Int center)
        {
            // Accelerate resource generation in area
            var affectedPositions = GameUtils.GetPositionsInRange(center, 1, gridSystem.gridWidth, gridSystem.gridHeight);
            
            EventSystem.Publish(new SpecialEffectEvent
            {
                effectType = SpecialEffectType.Regeneration,
                position = center,
                affectedPositions = affectedPositions
            });
        }

        private void TriggerElectronicReaction(Vector2Int center)
        {
            // Electronic surge effect
            EventSystem.Publish(new SpecialEffectEvent
            {
                effectType = SpecialEffectType.ChainPurification,
                position = center,
                affectedPositions = new List<Vector2Int> { center }
            });
        }

        private void CheckChainReactions(List<Vector2Int> matchedBlocks)
        {
            // Check if the match triggers additional matches
            foreach (var position in matchedBlocks)
            {
                var neighbors = GameUtils.GetNeighbors(position, gridSystem.gridWidth, gridSystem.gridHeight);
                foreach (var neighbor in neighbors)
                {
                    var neighborBlock = gridSystem.GetWasteBlock(neighbor);
                    if (neighborBlock != null && neighborBlock.blockType == WasteBlockType.ExplosiveWaste)
                    {
                        // Trigger explosive chain reaction
                        StartCoroutine(TriggerDelayedExplosion(neighbor, 0.5f));
                    }
                }
            }
        }

        private IEnumerator TriggerDelayedExplosion(Vector2Int position, float delay)
        {
            yield return new WaitForSeconds(delay);
            
            var wasteBlock = gridSystem.GetWasteBlock(position);
            if (wasteBlock != null)
            {
                wasteBlock.OnDestroyed();
                // Remove the block and trigger its explosion effect
                gridSystem.SetWasteBlock(position, null);
                Destroy(wasteBlock.gameObject);
            }
        }

        private void TriggerSpecialBlockEffect(WasteBlock wasteBlock, Vector2Int position)
        {
            wasteBlock.OnDestroyed();
        }

        private void PlayMatchEffect(List<Vector2Int> matchedBlocks)
        {
            if (matchEffect != null && matchedBlocks.Count > 0)
            {
                Vector3 centerPos = gridSystem.GridToWorldPosition(matchedBlocks[0]);
                ParticleSystem effect = Instantiate(matchEffect, centerPos, Quaternion.identity);
                effect.Play();
                Destroy(effect.gameObject, 2f);
            }

            if (audioSource != null && matchSound != null)
            {
                audioSource.PlayOneShot(matchSound);
            }
        }

        private void PlayComboEffect(List<Vector2Int> matchedBlocks)
        {
            if (comboEffect != null && matchedBlocks.Count > 0)
            {
                Vector3 centerPos = gridSystem.GridToWorldPosition(matchedBlocks[0]);
                ParticleSystem effect = Instantiate(comboEffect, centerPos, Quaternion.identity);
                effect.Play();
                Destroy(effect.gameObject, 3f);
            }

            if (audioSource != null && comboSound != null)
            {
                audioSource.PlayOneShot(comboSound);
            }
        }

        private void OnMatchProcessed(MatchEvent matchEvent)
        {
            // Additional processing after match is completed
            Debug.Log($"Match processed: {matchEvent.matchedBlocks.Count} blocks, Combo: {matchEvent.combo}");
        }

        public int GetCurrentCombo()
        {
            return currentCombo;
        }

        public float GetComboTimeRemaining()
        {
            return comboTimer;
        }

        private void OnDestroy()
        {
            EventSystem.Unsubscribe<MatchEvent>(OnMatchProcessed);
        }
    }
}
