using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace WastelandReclaim
{
    /// <summary>
    /// 游戏玩法增强器 - 添加更多策略深度和趣味性
    /// </summary>
    public class GameplayEnhancer : MonoBehaviour
    {
        [Header("增强功能")]
        public bool enableComboSystem = true;
        public bool enableSpecialEvents = true;
        public bool enableDynamicDifficulty = true;
        public bool enableAchievementSystem = true;

        [Header("连击系统")]
        public float comboTimeWindow = 3f;
        public int[] comboThresholds = { 3, 5, 8, 12, 20 };
        public string[] comboNames = { "不错", "很好", "优秀", "完美", "传奇" };
        public float[] comboMultipliers = { 1.2f, 1.5f, 2f, 3f, 5f };

        [Header("特殊事件")]
        public float eventChance = 0.15f;
        public float eventDuration = 10f;

        [Header("动态难度")]
        public float difficultyAdjustmentRate = 0.1f;
        public float playerSkillTrackingWindow = 60f;

        private int currentCombo = 0;
        private float lastMatchTime = 0f;
        private Queue<float> recentPerformance = new Queue<float>();
        private float currentDifficultyMultiplier = 1f;
        private List<Achievement> unlockedAchievements = new List<Achievement>();
        private SpecialEvent currentEvent = null;

        public System.Action<int, string> OnComboAchieved;
        public System.Action<SpecialEvent> OnSpecialEventStarted;
        public System.Action<Achievement> OnAchievementUnlocked;

        private void Start()
        {
            SetupEventListeners();
            InitializeAchievements();
        }

        private void SetupEventListeners()
        {
            EventSystem.Subscribe<MatchEvent>(OnMatch);
            EventSystem.Subscribe<ScoreEvent>(OnScore);
            EventSystem.Subscribe<LevelCompleteEvent>(OnLevelComplete);
        }

        private void OnMatch(MatchEvent matchEvent)
        {
            if (enableComboSystem)
            {
                UpdateComboSystem();
            }

            if (enableSpecialEvents && Random.value < eventChance)
            {
                TriggerRandomEvent();
            }

            TrackPlayerPerformance(matchEvent.matchedBlocks.Count);
            CheckAchievements(matchEvent);
        }

        private void OnScore(ScoreEvent scoreEvent)
        {
            if (enableDynamicDifficulty)
            {
                UpdateDynamicDifficulty(scoreEvent.points);
            }
        }

        private void OnLevelComplete(LevelCompleteEvent levelEvent)
        {
            ResetCombo();
            EvaluateLevelPerformance(levelEvent);
        }

        #region 连击系统
        private void UpdateComboSystem()
        {
            float currentTime = Time.time;
            
            if (currentTime - lastMatchTime <= comboTimeWindow)
            {
                currentCombo++;
            }
            else
            {
                currentCombo = 1;
            }

            lastMatchTime = currentTime;

            // 检查连击里程碑
            for (int i = 0; i < comboThresholds.Length; i++)
            {
                if (currentCombo == comboThresholds[i])
                {
                    TriggerComboMilestone(i);
                    break;
                }
            }

            // 发布连击事件
            EventSystem.Publish(new ComboUpdateEvent 
            { 
                comboCount = currentCombo,
                multiplier = GetComboMultiplier()
            });
        }

        private void TriggerComboMilestone(int milestoneIndex)
        {
            string comboName = comboNames[milestoneIndex];
            float multiplier = comboMultipliers[milestoneIndex];

            OnComboAchieved?.Invoke(currentCombo, comboName);

            // 创建特殊奖励
            CreateComboReward(milestoneIndex);

            Debug.Log($"连击里程碑达成: {comboName} (x{currentCombo})");
        }

        private void CreateComboReward(int milestoneIndex)
        {
            // 根据连击等级给予不同奖励
            switch (milestoneIndex)
            {
                case 0: // 3连击 - 额外分数
                    EventSystem.Publish(new ScoreEvent 
                    { 
                        points = 50, 
                        position = Vector3.zero 
                    });
                    break;

                case 1: // 5连击 - 资源奖励
                    Dictionary<ResourceType, int> resources = new Dictionary<ResourceType, int>
                    {
                        { ResourceType.Metal, 5 },
                        { ResourceType.Plastic, 5 }
                    };
                    EventSystem.Publish(new ResourceGainEvent { resources = resources });
                    break;

                case 2: // 8连击 - 特殊块生成
                    CreateSpecialBlockReward();
                    break;

                case 3: // 12连击 - 时间奖励
                    ExtendTimeLimit(10f);
                    break;

                case 4: // 20连击 - 超级奖励
                    TriggerSuperReward();
                    break;
            }
        }

        private float GetComboMultiplier()
        {
            if (currentCombo <= 1) return 1f;

            for (int i = comboThresholds.Length - 1; i >= 0; i--)
            {
                if (currentCombo >= comboThresholds[i])
                {
                    return comboMultipliers[i];
                }
            }

            return 1f;
        }

        private void ResetCombo()
        {
            currentCombo = 0;
            lastMatchTime = 0f;
        }
        #endregion

        #region 特殊事件系统
        private void TriggerRandomEvent()
        {
            if (currentEvent != null) return; // 已有事件在进行中

            SpecialEventType[] eventTypes = {
                SpecialEventType.DoubleScore,
                SpecialEventType.ResourceBoost,
                SpecialEventType.TimeFreeze,
                SpecialEventType.LuckyBlocks,
                SpecialEventType.ChainReaction
            };

            SpecialEventType selectedType = eventTypes[Random.Range(0, eventTypes.Length)];
            StartSpecialEvent(selectedType);
        }

        private void StartSpecialEvent(SpecialEventType eventType)
        {
            currentEvent = new SpecialEvent
            {
                type = eventType,
                duration = eventDuration,
                startTime = Time.time
            };

            OnSpecialEventStarted?.Invoke(currentEvent);

            switch (eventType)
            {
                case SpecialEventType.DoubleScore:
                    Debug.Log("特殊事件：双倍分数！");
                    break;
                case SpecialEventType.ResourceBoost:
                    Debug.Log("特殊事件：资源加成！");
                    break;
                case SpecialEventType.TimeFreeze:
                    Debug.Log("特殊事件：时间冻结！");
                    break;
                case SpecialEventType.LuckyBlocks:
                    Debug.Log("特殊事件：幸运方块！");
                    break;
                case SpecialEventType.ChainReaction:
                    Debug.Log("特殊事件：连锁反应！");
                    break;
            }

            StartCoroutine(HandleSpecialEvent());
        }

        private IEnumerator HandleSpecialEvent()
        {
            yield return new WaitForSeconds(eventDuration);
            EndSpecialEvent();
        }

        private void EndSpecialEvent()
        {
            if (currentEvent != null)
            {
                Debug.Log($"特殊事件结束：{currentEvent.type}");
                currentEvent = null;
            }
        }

        public bool IsEventActive(SpecialEventType eventType)
        {
            return currentEvent != null && currentEvent.type == eventType;
        }
        #endregion

        #region 动态难度系统
        private void TrackPlayerPerformance(int matchSize)
        {
            float performance = CalculatePerformanceScore(matchSize);
            recentPerformance.Enqueue(performance);

            // 保持性能数据在时间窗口内
            while (recentPerformance.Count > playerSkillTrackingWindow)
            {
                recentPerformance.Dequeue();
            }
        }

        private float CalculatePerformanceScore(int matchSize)
        {
            // 基于消除块数量、连击等计算性能分数
            float baseScore = matchSize;
            float comboBonus = currentCombo * 0.5f;
            return baseScore + comboBonus;
        }

        private void UpdateDynamicDifficulty(int score)
        {
            if (recentPerformance.Count < 10) return; // 需要足够的数据

            float averagePerformance = 0f;
            foreach (float performance in recentPerformance)
            {
                averagePerformance += performance;
            }
            averagePerformance /= recentPerformance.Count;

            // 根据平均表现调整难度
            if (averagePerformance > 8f) // 表现很好，增加难度
            {
                currentDifficultyMultiplier += difficultyAdjustmentRate;
            }
            else if (averagePerformance < 4f) // 表现不佳，降低难度
            {
                currentDifficultyMultiplier -= difficultyAdjustmentRate;
            }

            currentDifficultyMultiplier = Mathf.Clamp(currentDifficultyMultiplier, 0.5f, 2f);
        }

        public float GetCurrentDifficultyMultiplier()
        {
            return currentDifficultyMultiplier;
        }
        #endregion

        #region 成就系统
        private void InitializeAchievements()
        {
            // 这里可以从配置文件或数据库加载成就
            // 现在先创建一些基础成就
        }

        private void CheckAchievements(MatchEvent matchEvent)
        {
            // 检查各种成就条件
            CheckComboAchievements();
            CheckMatchSizeAchievements(matchEvent.matchedBlocks.Count);
            CheckResourceAchievements();
        }

        private void CheckComboAchievements()
        {
            if (currentCombo >= 10 && !HasAchievement("combo_master"))
            {
                UnlockAchievement(new Achievement
                {
                    id = "combo_master",
                    name = "连击大师",
                    description = "达成10连击",
                    icon = "combo_icon"
                });
            }
        }

        private void CheckMatchSizeAchievements(int matchSize)
        {
            if (matchSize >= 7 && !HasAchievement("big_match"))
            {
                UnlockAchievement(new Achievement
                {
                    id = "big_match",
                    name = "大清理",
                    description = "一次消除7个或更多块",
                    icon = "big_match_icon"
                });
            }
        }

        private void CheckResourceAchievements()
        {
            // 检查资源相关成就
        }

        private bool HasAchievement(string achievementId)
        {
            return unlockedAchievements.Exists(a => a.id == achievementId);
        }

        private void UnlockAchievement(Achievement achievement)
        {
            unlockedAchievements.Add(achievement);
            OnAchievementUnlocked?.Invoke(achievement);
            Debug.Log($"成就解锁: {achievement.name} - {achievement.description}");
        }
        #endregion

        #region 奖励系统
        private void CreateSpecialBlockReward()
        {
            // 在随机位置创建特殊块
            GridSystem gridSystem = FindObjectOfType<GridSystem>();
            if (gridSystem != null)
            {
                Vector2Int randomPos = new Vector2Int(
                    Random.Range(0, gridSystem.gridWidth),
                    Random.Range(0, gridSystem.gridHeight)
                );

                if (gridSystem.GetWasteBlock(randomPos) == null)
                {
                    WasteBlockType specialType = Random.value > 0.5f ? 
                        WasteBlockType.Bomb : WasteBlockType.LineHorizontal;
                    gridSystem.CreateSpecialWasteBlock(randomPos, specialType);
                }
            }
        }

        private void ExtendTimeLimit(float additionalTime)
        {
            EventSystem.Publish(new TimeExtensionEvent { additionalTime = additionalTime });
        }

        private void TriggerSuperReward()
        {
            // 超级奖励：多种奖励组合
            EventSystem.Publish(new ScoreEvent { points = 500, position = Vector3.zero });
            
            Dictionary<ResourceType, int> superResources = new Dictionary<ResourceType, int>
            {
                { ResourceType.Metal, 20 },
                { ResourceType.Plastic, 20 },
                { ResourceType.Core, 5 }
            };
            EventSystem.Publish(new ResourceGainEvent { resources = superResources });

            CreateSpecialBlockReward();
            ExtendTimeLimit(15f);
        }
        #endregion

        private void EvaluateLevelPerformance(LevelCompleteEvent levelEvent)
        {
            // 评估关卡表现，可能解锁新成就或调整难度
            float performanceRating = CalculateLevelPerformance(levelEvent);
            
            if (performanceRating >= 0.9f)
            {
                Debug.Log("完美表现！");
                // 给予额外奖励
            }
            else if (performanceRating >= 0.7f)
            {
                Debug.Log("优秀表现！");
            }
            else if (performanceRating >= 0.5f)
            {
                Debug.Log("良好表现！");
            }
        }

        private float CalculateLevelPerformance(LevelCompleteEvent levelEvent)
        {
            // 基于多个因素计算表现评分
            float scoreRating = Mathf.Clamp01(levelEvent.finalScore / 1000f);
            float timeRating = levelEvent.timeRemaining > 0 ? 1f : 0.5f;
            float efficiencyRating = Mathf.Clamp01(1f - (levelEvent.stepsUsed / 50f));

            return (scoreRating + timeRating + efficiencyRating) / 3f;
        }

        private void OnDestroy()
        {
            EventSystem.Unsubscribe<MatchEvent>(OnMatch);
            EventSystem.Unsubscribe<ScoreEvent>(OnScore);
            EventSystem.Unsubscribe<LevelCompleteEvent>(OnLevelComplete);
        }
    }

    // 新的事件类型
    public class ComboUpdateEvent
    {
        public int comboCount;
        public float multiplier;
    }

    public class TimeExtensionEvent
    {
        public float additionalTime;
    }

    public class LevelCompleteEvent
    {
        public int finalScore;
        public float timeRemaining;
        public int stepsUsed;
        public bool isPerfect;
    }

    // 特殊事件
    public enum SpecialEventType
    {
        DoubleScore,
        ResourceBoost,
        TimeFreeze,
        LuckyBlocks,
        ChainReaction
    }

    public class SpecialEvent
    {
        public SpecialEventType type;
        public float duration;
        public float startTime;
    }

    // 成就系统
    public class Achievement
    {
        public string id;
        public string name;
        public string description;
        public string icon;
        public bool isUnlocked;
    }
}
