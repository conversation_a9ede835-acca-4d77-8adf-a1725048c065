using UnityEngine;

namespace WastelandReclaim
{
    /// <summary>
    /// 材质和纹理生成器 - 创建高品质的游戏材质
    /// </summary>
    public static class MaterialGenerator
    {
        /// <summary>
        /// 创建废料块材质
        /// </summary>
        public static Material CreateWasteBlockMaterial(WasteBlockType wasteType)
        {
            Material material = new Material(Shader.Find("Standard"));
            
            switch (wasteType)
            {
                case WasteBlockType.Metal:
                    material.color = new Color(0.7f, 0.7f, 0.8f);
                    material.metallic = 0.8f;
                    material.smoothness = 0.6f;
                    break;
                    
                case WasteBlockType.Plastic:
                    material.color = new Color(0.2f, 0.6f, 1f);
                    material.metallic = 0.1f;
                    material.smoothness = 0.8f;
                    break;
                    
                case WasteBlockType.Wood:
                    material.color = new Color(0.6f, 0.4f, 0.2f);
                    material.metallic = 0f;
                    material.smoothness = 0.2f;
                    break;
                    
                case WasteBlockType.Electronic:
                    material.color = new Color(0.2f, 0.8f, 0.2f);
                    material.metallic = 0.3f;
                    material.smoothness = 0.5f;
                    material.EnableKeyword("_EMISSION");
                    material.SetColor("_EmissionColor", new Color(0.1f, 0.4f, 0.1f));
                    break;
                    
                case WasteBlockType.Glass:
                    material.color = new Color(0.9f, 0.9f, 0.9f, 0.7f);
                    material.metallic = 0f;
                    material.smoothness = 0.95f;
                    material.SetFloat("_Mode", 3); // Transparent
                    material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
                    material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
                    material.SetInt("_ZWrite", 0);
                    material.EnableKeyword("_ALPHABLEND_ON");
                    material.renderQueue = 3000;
                    break;
                    
                case WasteBlockType.Organic:
                    material.color = new Color(1f, 0.8f, 0.2f);
                    material.metallic = 0f;
                    material.smoothness = 0.3f;
                    break;
                    
                case WasteBlockType.Bomb:
                    material.color = new Color(1f, 0.3f, 0.1f);
                    material.metallic = 0.2f;
                    material.smoothness = 0.4f;
                    material.EnableKeyword("_EMISSION");
                    material.SetColor("_EmissionColor", new Color(0.5f, 0.1f, 0.05f));
                    break;
                    
                case WasteBlockType.LineHorizontal:
                case WasteBlockType.LineVertical:
                    material.color = new Color(0.8f, 0.2f, 0.8f);
                    material.metallic = 0.5f;
                    material.smoothness = 0.7f;
                    material.EnableKeyword("_EMISSION");
                    material.SetColor("_EmissionColor", new Color(0.4f, 0.1f, 0.4f));
                    break;
                    
                case WasteBlockType.ColorBomb:
                    material.color = Color.white;
                    material.metallic = 0.8f;
                    material.smoothness = 0.9f;
                    material.EnableKeyword("_EMISSION");
                    material.SetColor("_EmissionColor", new Color(0.5f, 0.5f, 0.5f));
                    break;
                    
                default:
                    material.color = Color.gray;
                    break;
            }
            
            return material;
        }

        /// <summary>
        /// 创建防御塔材质
        /// </summary>
        public static Material CreateTowerMaterial(TowerType towerType)
        {
            Material material = new Material(Shader.Find("Standard"));
            
            switch (towerType)
            {
                case TowerType.WindFilter:
                    material.color = new Color(0.7f, 0.9f, 1f);
                    material.metallic = 0.6f;
                    material.smoothness = 0.8f;
                    break;
                    
                case TowerType.SonicRepeller:
                    material.color = new Color(1f, 0.8f, 0.2f);
                    material.metallic = 0.4f;
                    material.smoothness = 0.6f;
                    material.EnableKeyword("_EMISSION");
                    material.SetColor("_EmissionColor", new Color(0.3f, 0.2f, 0.05f));
                    break;
                    
                case TowerType.ShieldGenerator:
                    material.color = new Color(0.2f, 0.8f, 0.2f);
                    material.metallic = 0.3f;
                    material.smoothness = 0.7f;
                    material.EnableKeyword("_EMISSION");
                    material.SetColor("_EmissionColor", new Color(0.1f, 0.3f, 0.1f));
                    break;
                    
                case TowerType.ResourceAccelerator:
                    material.color = new Color(0.8f, 0.2f, 0.8f);
                    material.metallic = 0.7f;
                    material.smoothness = 0.9f;
                    material.EnableKeyword("_EMISSION");
                    material.SetColor("_EmissionColor", new Color(0.3f, 0.1f, 0.3f));
                    break;
                    
                case TowerType.LaserTurret:
                    material.color = new Color(1f, 0.2f, 0.2f);
                    material.metallic = 0.8f;
                    material.smoothness = 0.8f;
                    material.EnableKeyword("_EMISSION");
                    material.SetColor("_EmissionColor", new Color(0.4f, 0.1f, 0.1f));
                    break;
                    
                case TowerType.ElectromagneticNet:
                    material.color = new Color(0.2f, 0.2f, 1f);
                    material.metallic = 0.9f;
                    material.smoothness = 0.95f;
                    material.EnableKeyword("_EMISSION");
                    material.SetColor("_EmissionColor", new Color(0.1f, 0.1f, 0.4f));
                    break;
                    
                default:
                    material.color = Color.gray;
                    break;
            }
            
            return material;
        }

        /// <summary>
        /// 创建网格材质
        /// </summary>
        public static Material CreateGridMaterial()
        {
            Material material = new Material(Shader.Find("Standard"));
            material.color = new Color(1f, 1f, 1f, 0.3f);
            material.metallic = 0f;
            material.smoothness = 0.1f;
            
            // 设置透明模式
            material.SetFloat("_Mode", 3);
            material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            material.SetInt("_ZWrite", 0);
            material.DisableKeyword("_ALPHATEST_ON");
            material.EnableKeyword("_ALPHABLEND_ON");
            material.DisableKeyword("_ALPHAPREMULTIPLY_ON");
            material.renderQueue = 3000;
            
            return material;
        }

        /// <summary>
        /// 创建发光材质
        /// </summary>
        public static Material CreateGlowMaterial(Color glowColor, float intensity = 1f)
        {
            Material material = new Material(Shader.Find("Standard"));
            material.color = glowColor;
            material.EnableKeyword("_EMISSION");
            material.SetColor("_EmissionColor", glowColor * intensity);
            material.metallic = 0f;
            material.smoothness = 0.8f;
            
            return material;
        }

        /// <summary>
        /// 创建全息材质
        /// </summary>
        public static Material CreateHologramMaterial()
        {
            Material material = new Material(Shader.Find("Standard"));
            material.color = new Color(0.2f, 0.8f, 1f, 0.5f);
            material.EnableKeyword("_EMISSION");
            material.SetColor("_EmissionColor", new Color(0.1f, 0.4f, 0.5f));
            
            // 设置透明模式
            material.SetFloat("_Mode", 3);
            material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            material.SetInt("_ZWrite", 0);
            material.EnableKeyword("_ALPHABLEND_ON");
            material.renderQueue = 3000;
            
            return material;
        }

        /// <summary>
        /// 创建程序化纹理
        /// </summary>
        public static Texture2D CreateProceduralTexture(int width, int height, System.Func<float, float, Color> colorFunction)
        {
            Texture2D texture = new Texture2D(width, height);
            
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    float u = (float)x / width;
                    float v = (float)y / height;
                    Color color = colorFunction(u, v);
                    texture.SetPixel(x, y, color);
                }
            }
            
            texture.Apply();
            return texture;
        }

        /// <summary>
        /// 创建噪声纹理
        /// </summary>
        public static Texture2D CreateNoiseTexture(int width, int height, float scale = 1f)
        {
            return CreateProceduralTexture(width, height, (u, v) =>
            {
                float noise = Mathf.PerlinNoise(u * scale, v * scale);
                return new Color(noise, noise, noise, 1f);
            });
        }

        /// <summary>
        /// 创建渐变纹理
        /// </summary>
        public static Texture2D CreateGradientTexture(int width, int height, Color startColor, Color endColor, bool horizontal = true)
        {
            return CreateProceduralTexture(width, height, (u, v) =>
            {
                float t = horizontal ? u : v;
                return Color.Lerp(startColor, endColor, t);
            });
        }

        /// <summary>
        /// 创建棋盘纹理
        /// </summary>
        public static Texture2D CreateCheckerboardTexture(int width, int height, Color color1, Color color2, int checkerSize = 8)
        {
            return CreateProceduralTexture(width, height, (u, v) =>
            {
                int x = Mathf.FloorToInt(u * width / checkerSize);
                int y = Mathf.FloorToInt(v * height / checkerSize);
                return (x + y) % 2 == 0 ? color1 : color2;
            });
        }

        /// <summary>
        /// 创建废土风格的天空盒纹理
        /// </summary>
        public static Texture2D CreateWastelandSkyTexture(int width, int height)
        {
            return CreateProceduralTexture(width, height, (u, v) =>
            {
                // 基础渐变：从地平线的橙红色到天空的暗蓝色
                Color horizonColor = new Color(0.8f, 0.4f, 0.2f); // 橙红色
                Color skyColor = new Color(0.2f, 0.3f, 0.5f);     // 暗蓝色
                
                // 添加一些噪声来模拟云层和污染
                float noise1 = Mathf.PerlinNoise(u * 3f, v * 2f);
                float noise2 = Mathf.PerlinNoise(u * 8f, v * 5f) * 0.3f;
                float totalNoise = (noise1 + noise2) * 0.3f;
                
                // 垂直渐变
                float t = v + totalNoise;
                t = Mathf.Clamp01(t);
                
                Color baseColor = Color.Lerp(horizonColor, skyColor, t);
                
                // 添加一些污染粒子效果
                if (noise1 > 0.7f && noise2 > 0.2f)
                {
                    baseColor = Color.Lerp(baseColor, new Color(0.6f, 0.5f, 0.3f), 0.4f);
                }
                
                return baseColor;
            });
        }

        /// <summary>
        /// 创建金属废料纹理
        /// </summary>
        public static Texture2D CreateMetalWasteTexture(int width, int height)
        {
            return CreateProceduralTexture(width, height, (u, v) =>
            {
                // 基础金属色
                Color baseColor = new Color(0.7f, 0.7f, 0.8f);
                
                // 添加锈迹
                float rust = Mathf.PerlinNoise(u * 10f, v * 10f);
                if (rust > 0.6f)
                {
                    Color rustColor = new Color(0.6f, 0.3f, 0.1f);
                    baseColor = Color.Lerp(baseColor, rustColor, (rust - 0.6f) * 2.5f);
                }
                
                // 添加划痕
                float scratch = Mathf.PerlinNoise(u * 50f, v * 2f);
                if (scratch > 0.8f)
                {
                    baseColor = Color.Lerp(baseColor, Color.black, 0.3f);
                }
                
                return baseColor;
            });
        }

        /// <summary>
        /// 创建塑料废料纹理
        /// </summary>
        public static Texture2D CreatePlasticWasteTexture(int width, int height)
        {
            return CreateProceduralTexture(width, height, (u, v) =>
            {
                // 基础塑料色
                Color baseColor = new Color(0.2f, 0.6f, 1f);
                
                // 添加磨损效果
                float wear = Mathf.PerlinNoise(u * 15f, v * 15f);
                if (wear > 0.5f)
                {
                    baseColor = Color.Lerp(baseColor, Color.white, (wear - 0.5f) * 0.6f);
                }
                
                // 添加污渍
                float stain = Mathf.PerlinNoise(u * 8f, v * 8f);
                if (stain > 0.7f)
                {
                    Color stainColor = new Color(0.4f, 0.3f, 0.2f);
                    baseColor = Color.Lerp(baseColor, stainColor, (stain - 0.7f) * 1.5f);
                }
                
                return baseColor;
            });
        }

        /// <summary>
        /// 应用材质到游戏对象
        /// </summary>
        public static void ApplyMaterialToGameObject(GameObject obj, Material material)
        {
            Renderer renderer = obj.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material = material;
            }
        }

        /// <summary>
        /// 创建动画材质（随时间变化的发光效果）
        /// </summary>
        public static Material CreateAnimatedGlowMaterial(Color baseColor, Color glowColor, float speed = 1f)
        {
            Material material = CreateGlowMaterial(glowColor);
            material.color = baseColor;
            
            // 这里可以添加一个脚本组件来处理动画
            // 或者使用Shader Graph来创建更复杂的动画效果
            
            return material;
        }
    }
}
