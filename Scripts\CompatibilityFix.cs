using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace WastelandReclaim
{
    /// <summary>
    /// Unity 2021.3 兼容性修复脚本
    /// 解决常见的编译错误和兼容性问题
    /// </summary>
    public class CompatibilityFix : MonoBehaviour
    {
        [Header("兼容性检查")]
        public bool autoFixOnStart = true;
        public bool showFixResults = true;

        private void Start()
        {
            if (autoFixOnStart)
            {
                StartCoroutine(PerformCompatibilityCheck());
            }
        }

        private IEnumerator PerformCompatibilityCheck()
        {
            Debug.Log("=== 开始兼容性检查 ===");

            // 检查Unity版本
            CheckUnityVersion();
            yield return null;

            // 检查脚本编译状态
            CheckScriptCompilation();
            yield return null;

            // 检查必要组件
            CheckRequiredComponents();
            yield return null;

            // 修复常见问题
            FixCommonIssues();
            yield return null;

            Debug.Log("=== 兼容性检查完成 ===");
        }

        private void CheckUnityVersion()
        {
            string unityVersion = Application.unityVersion;
            Debug.Log($"Unity版本: {unityVersion}");

            if (unityVersion.StartsWith("2021.3"))
            {
                Debug.Log("✓ Unity版本兼容");
            }
            else
            {
                Debug.LogWarning($"⚠️ 当前Unity版本 {unityVersion}，推荐使用2021.3.x");
            }
        }

        private void CheckScriptCompilation()
        {
            // 检查是否有编译错误
            bool hasCompilationErrors = false;

            #if UNITY_EDITOR
            // 在编辑器中检查编译状态
            if (UnityEditor.EditorApplication.isCompiling)
            {
                Debug.Log("⏳ 脚本正在编译中...");
                hasCompilationErrors = true;
            }
            #endif

            if (!hasCompilationErrors)
            {
                Debug.Log("✓ 脚本编译正常");
            }
        }

        private void CheckRequiredComponents()
        {
            Debug.Log("检查必要组件...");

            // 检查GameManager
            if (GameManager.Instance != null)
            {
                Debug.Log("✓ GameManager 已创建");
            }
            else
            {
                Debug.LogWarning("⚠️ GameManager 未找到");
            }

            // 检查其他系统
            CheckSystem<GridSystem>("GridSystem");
            CheckSystem<MatchSystem>("MatchSystem");
            CheckSystem<TowerSystem>("TowerSystem");
            CheckSystem<EnemySystem>("EnemySystem");
            CheckSystem<UIManager>("UIManager");
            CheckSystem<AudioManager>("AudioManager");
        }

        private void CheckSystem<T>(string systemName) where T : MonoBehaviour
        {
            T system = FindObjectOfType<T>();
            if (system != null)
            {
                Debug.Log($"✓ {systemName} 已创建");
            }
            else
            {
                Debug.LogWarning($"⚠️ {systemName} 未找到");
            }
        }

        private void FixCommonIssues()
        {
            Debug.Log("修复常见问题...");

            // 修复相机设置
            FixCameraSettings();

            // 修复物理设置
            FixPhysicsSettings();

            // 修复渲染设置
            FixRenderSettings();
        }

        private void FixCameraSettings()
        {
            Camera mainCamera = Camera.main;
            if (mainCamera == null)
            {
                Debug.LogWarning("⚠️ 主相机未找到，正在创建...");
                GameObject cameraObj = new GameObject("Main Camera");
                mainCamera = cameraObj.AddComponent<Camera>();
                cameraObj.tag = "MainCamera";
            }

            // 确保相机设置正确
            if (mainCamera.transform.position == Vector3.zero)
            {
                mainCamera.transform.position = new Vector3(4, 10, -8);
                mainCamera.transform.eulerAngles = new Vector3(45, 0, 0);
                Debug.Log("✓ 相机位置已修复");
            }
        }

        private void FixPhysicsSettings()
        {
            // 确保物理设置正确
            if (Physics.gravity.y > -9f)
            {
                Physics.gravity = new Vector3(0, -9.81f, 0);
                Debug.Log("✓ 重力设置已修复");
            }
        }

        private void FixRenderSettings()
        {
            // 设置基础渲染设置
            if (RenderSettings.ambientMode != UnityEngine.Rendering.AmbientMode.Trilight)
            {
                RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Trilight;
                RenderSettings.ambientSkyColor = new Color(0.5f, 0.7f, 1f);
                RenderSettings.ambientEquatorColor = new Color(0.4f, 0.4f, 0.4f);
                RenderSettings.ambientGroundColor = new Color(0.2f, 0.2f, 0.2f);
                Debug.Log("✓ 渲染设置已修复");
            }
        }

        // 手动修复方法
        [ContextMenu("手动运行兼容性检查")]
        public void ManualCompatibilityCheck()
        {
            StartCoroutine(PerformCompatibilityCheck());
        }

        [ContextMenu("重新创建所有系统")]
        public void RecreateAllSystems()
        {
            Debug.Log("重新创建所有系统...");

            // 删除现有系统
            DestroyExistingSystems();

            // 重新创建系统
            StartCoroutine(RecreateSystemsCoroutine());
        }

        private void DestroyExistingSystems()
        {
            // 销毁现有系统（除了GameManager）
            GridSystem[] gridSystems = FindObjectsOfType<GridSystem>();
            foreach (var system in gridSystems)
            {
                if (system.gameObject != gameObject)
                    DestroyImmediate(system.gameObject);
            }

            MatchSystem[] matchSystems = FindObjectsOfType<MatchSystem>();
            foreach (var system in matchSystems)
            {
                if (system.gameObject != gameObject)
                    DestroyImmediate(system.gameObject);
            }

            // 继续销毁其他系统...
        }

        private IEnumerator RecreateSystemsCoroutine()
        {
            yield return new WaitForSeconds(0.1f);

            // 重新创建系统
            SimpleGameStarter gameStarter = FindObjectOfType<SimpleGameStarter>();
            if (gameStarter != null)
            {
                Debug.Log("找到SimpleGameStarter，重新初始化...");
                // 简单重启
                gameStarter.gameObject.SetActive(false);
                yield return new WaitForSeconds(0.1f);
                gameStarter.gameObject.SetActive(true);
            }
            else
            {
                Debug.LogWarning("SimpleGameStarter组件未找到，请手动添加");
            }
        }

        // 诊断信息显示
        private void OnGUI()
        {
            if (showFixResults)
            {
                GUILayout.BeginArea(new Rect(Screen.width - 300, 10, 290, 200));
                GUILayout.BeginVertical("box");

                GUILayout.Label("=== 兼容性状态 ===", new GUIStyle(GUI.skin.label) { fontStyle = FontStyle.Bold });

                // 显示Unity版本
                GUILayout.Label($"Unity: {Application.unityVersion}");

                // 显示系统状态
                GUILayout.Label($"GameManager: {(GameManager.Instance != null ? "✓" : "✗")}");
                GUILayout.Label($"GridSystem: {(FindObjectOfType<GridSystem>() != null ? "✓" : "✗")}");
                GUILayout.Label($"MatchSystem: {(FindObjectOfType<MatchSystem>() != null ? "✓" : "✗")}");
                GUILayout.Label($"TowerSystem: {(FindObjectOfType<TowerSystem>() != null ? "✓" : "✗")}");

                GUILayout.Space(10);

                if (GUILayout.Button("重新检查"))
                {
                    ManualCompatibilityCheck();
                }

                if (GUILayout.Button("重建系统"))
                {
                    RecreateAllSystems();
                }

                GUILayout.EndVertical();
                GUILayout.EndArea();
            }
        }

        // 常见错误解决方案
        public static class ErrorSolutions
        {
            public static void FixNamespaceIssues()
            {
                Debug.Log("命名空间问题解决方案：");
                Debug.Log("1. 确保所有脚本都在 WastelandReclaim 命名空间中");
                Debug.Log("2. 检查 using 语句是否正确");
                Debug.Log("3. 重新导入脚本文件");
            }

            public static void FixMissingReferences()
            {
                Debug.Log("缺失引用问题解决方案：");
                Debug.Log("1. 检查预制体引用是否正确设置");
                Debug.Log("2. 确保所有必要的组件都已添加");
                Debug.Log("3. 重新分配丢失的引用");
            }

            public static void FixCompilationErrors()
            {
                Debug.Log("编译错误解决方案：");
                Debug.Log("1. 检查语法错误");
                Debug.Log("2. 确保所有依赖项都已导入");
                Debug.Log("3. 清理并重新编译项目");
            }
        }
    }

    // 扩展方法已移除，避免编译冲突
}

// 扩展方法已移除，避免编译冲突
