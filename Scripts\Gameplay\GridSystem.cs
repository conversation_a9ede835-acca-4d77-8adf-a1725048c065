using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace WastelandReclaim
{
    public class GridSystem : MonoBehaviour
    {
        [Header("Grid Configuration")]
        public int gridWidth = 8;
        public int gridHeight = 8;
        public float cellSize = 1.0f;
        public Vector3 gridOrigin = Vector3.zero;

        [Header("Prefabs")]
        public GameObject cellPrefab;
        public GameObject[] wasteBlockPrefabs;
        public GameObject purifierPrefab;

        [Header("Visual")]
        public Material gridMaterial;
        public Color gridLineColor = Color.white;

        private WasteBlock[,] grid;
        private GameObject[,] cellObjects;
        private List<Vector2Int> selectedBlocks = new List<Vector2Int>();
        private GameObject purifierObject;
        private Vector2Int purifierPosition;

        // Events
        public System.Action<Vector2Int> OnCellClickedEvent;
        public System.Action<List<Vector2Int>> OnBlocksSelected;

        public void Initialize()
        {
            CreateGrid();
            CreateVisualGrid();
        }

        public void LoadLevel(LevelData levelData)
        {
            gridWidth = levelData.gridSize.x;
            gridHeight = levelData.gridSize.y;
            purifierPosition = levelData.purifierPosition;

            // Clear existing grid
            ClearGrid();
            
            // Create new grid
            CreateGrid();
            CreateVisualGrid();

            // Place purifier
            PlacePurifier(purifierPosition);

            // Generate waste blocks
            foreach (var wasteData in levelData.wasteBlocks)
            {
                CreateWasteBlock(wasteData);
            }

            // Block specified positions
            foreach (var blockedPos in levelData.blockedPositions)
            {
                BlockPosition(blockedPos);
            }
        }

        private void CreateGrid()
        {
            grid = new WasteBlock[gridWidth, gridHeight];
            cellObjects = new GameObject[gridWidth, gridHeight];
        }

        private void CreateVisualGrid()
        {
            // Create cell objects
            for (int x = 0; x < gridWidth; x++)
            {
                for (int y = 0; y < gridHeight; y++)
                {
                    Vector3 worldPos = GridToWorldPosition(new Vector2Int(x, y));
                    GameObject cellObj = Instantiate(cellPrefab, worldPos, Quaternion.identity, transform);
                    cellObj.name = $"Cell_{x}_{y}";
                    
                    // Add click handler
                    GridCell cellComponent = cellObj.GetComponent<GridCell>();
                    if (cellComponent == null)
                    {
                        cellComponent = cellObj.AddComponent<GridCell>();
                    }
                    cellComponent.Initialize(new Vector2Int(x, y), this);
                    
                    cellObjects[x, y] = cellObj;
                }
            }

            // Draw grid lines
            DrawGridLines();
        }

        private void DrawGridLines()
        {
            LineRenderer lineRenderer = GetComponent<LineRenderer>();
            if (lineRenderer == null)
            {
                lineRenderer = gameObject.AddComponent<LineRenderer>();
            }

            lineRenderer.material = gridMaterial;
            lineRenderer.color = gridLineColor;
            lineRenderer.startWidth = 0.05f;
            lineRenderer.endWidth = 0.05f;

            List<Vector3> points = new List<Vector3>();

            // Vertical lines
            for (int x = 0; x <= gridWidth; x++)
            {
                Vector3 start = GridToWorldPosition(new Vector2Int(x, 0)) - Vector3.right * cellSize * 0.5f;
                Vector3 end = GridToWorldPosition(new Vector2Int(x, gridHeight - 1)) + Vector3.up * cellSize * 0.5f;
                points.Add(start);
                points.Add(end);
            }

            // Horizontal lines
            for (int y = 0; y <= gridHeight; y++)
            {
                Vector3 start = GridToWorldPosition(new Vector2Int(0, y)) - Vector3.up * cellSize * 0.5f;
                Vector3 end = GridToWorldPosition(new Vector2Int(gridWidth - 1, y)) + Vector3.right * cellSize * 0.5f;
                points.Add(start);
                points.Add(end);
            }

            lineRenderer.positionCount = points.Count;
            lineRenderer.SetPositions(points.ToArray());
        }

        public Vector3 GridToWorldPosition(Vector2Int gridPos)
        {
            return gridOrigin + new Vector3(gridPos.x * cellSize, gridPos.y * cellSize, 0);
        }

        public Vector2Int WorldToGridPosition(Vector3 worldPos)
        {
            Vector3 localPos = worldPos - gridOrigin;
            int x = Mathf.RoundToInt(localPos.x / cellSize);
            int y = Mathf.RoundToInt(localPos.y / cellSize);
            return new Vector2Int(x, y);
        }

        public bool IsValidPosition(Vector2Int position)
        {
            return position.x >= 0 && position.x < gridWidth && 
                   position.y >= 0 && position.y < gridHeight;
        }

        public WasteBlock GetWasteBlock(Vector2Int position)
        {
            if (IsValidPosition(position))
            {
                return grid[position.x, position.y];
            }
            return null;
        }

        public void SetWasteBlock(Vector2Int position, WasteBlock wasteBlock)
        {
            if (IsValidPosition(position))
            {
                grid[position.x, position.y] = wasteBlock;
            }
        }

        public void CreateWasteBlock(WasteBlockData wasteData)
        {
            if (!IsValidPosition(wasteData.position))
                return;

            Vector3 worldPos = GridToWorldPosition(wasteData.position);
            GameObject prefab = GetWasteBlockPrefab(wasteData.type);
            
            if (prefab != null)
            {
                GameObject wasteObj = Instantiate(prefab, worldPos, Quaternion.identity, transform);
                WasteBlock wasteBlock = wasteObj.GetComponent<WasteBlock>();
                
                if (wasteBlock == null)
                {
                    wasteBlock = wasteObj.AddComponent<WasteBlock>();
                }

                wasteBlock.Initialize(wasteData);
                SetWasteBlock(wasteData.position, wasteBlock);
            }
        }

        private GameObject GetWasteBlockPrefab(WasteBlockType type)
        {
            int index = (int)type;
            if (index >= 0 && index < wasteBlockPrefabs.Length)
            {
                return wasteBlockPrefabs[index];
            }
            return wasteBlockPrefabs[0]; // Default to first prefab
        }

        public void PlacePurifier(Vector2Int position)
        {
            if (purifierObject != null)
            {
                DestroyImmediate(purifierObject);
            }

            Vector3 worldPos = GridToWorldPosition(position);
            purifierObject = Instantiate(purifierPrefab, worldPos, Quaternion.identity, transform);
            purifierPosition = position;
        }

        public void BlockPosition(Vector2Int position)
        {
            if (IsValidPosition(position))
            {
                GameObject cellObj = cellObjects[position.x, position.y];
                if (cellObj != null)
                {
                    GridCell cell = cellObj.GetComponent<GridCell>();
                    if (cell != null)
                    {
                        cell.SetBlocked(true);
                    }
                }
            }
        }

        public void OnCellClicked(Vector2Int position)
        {
            if (!IsValidPosition(position))
                return;

            WasteBlock wasteBlock = GetWasteBlock(position);
            if (wasteBlock != null)
            {
                // Try to match
                if (GameManager.Instance.TryUseStep())
                {
                    TryMatch(position);
                }
            }
            else
            {
                // Try to build tower
                TryBuildTower(position);
            }

            OnCellClickedEvent?.Invoke(position);
        }

        public bool TryMatch(Vector2Int position)
        {
            var matchedBlocks = FindMatches(position);
            if (matchedBlocks.Count >= 3)
            {
                ProcessMatch(matchedBlocks);
                return true;
            }
            return false;
        }

        private List<Vector2Int> FindMatches(Vector2Int startPos)
        {
            var matches = new List<Vector2Int>();
            var wasteBlock = GetWasteBlock(startPos);
            
            if (wasteBlock == null)
                return matches;

            var blockType = wasteBlock.blockType;
            var queue = new Queue<Vector2Int>();
            var visited = new HashSet<Vector2Int>();

            queue.Enqueue(startPos);
            visited.Add(startPos);

            while (queue.Count > 0)
            {
                var current = queue.Dequeue();
                matches.Add(current);

                // Check neighbors
                foreach (var direction in GameUtils.GetDirections())
                {
                    var neighbor = current + direction;
                    if (IsValidPosition(neighbor) && 
                        !visited.Contains(neighbor))
                    {
                        var neighborBlock = GetWasteBlock(neighbor);
                        if (neighborBlock != null && neighborBlock.blockType == blockType)
                        {
                            queue.Enqueue(neighbor);
                            visited.Add(neighbor);
                        }
                    }
                }
            }

            return matches;
        }

        private void ProcessMatch(List<Vector2Int> matchedBlocks)
        {
            if (matchedBlocks.Count == 0)
                return;

            // Calculate resources
            var resources = new Dictionary<ResourceType, int>();
            WasteBlockType blockType = WasteBlockType.Metal;
            
            foreach (var pos in matchedBlocks)
            {
                var wasteBlock = GetWasteBlock(pos);
                if (wasteBlock != null)
                {
                    blockType = wasteBlock.blockType;
                    var blockResources = wasteBlock.GetResources();
                    foreach (var kvp in blockResources)
                    {
                        if (!resources.ContainsKey(kvp.Key))
                            resources[kvp.Key] = 0;
                        resources[kvp.Key] += kvp.Value;
                    }

                    // Remove waste block
                    RemoveWasteBlock(pos);
                }
            }

            // Apply combo multiplier
            int combo = CalculateCombo(matchedBlocks.Count);
            float multiplier = 1f + (combo - 1) * 0.1f;
            
            foreach (var key in resources.Keys.ToList())
            {
                resources[key] = Mathf.RoundToInt(resources[key] * multiplier);
            }

            // Create match event
            var matchEvent = new MatchEvent
            {
                matchedBlocks = matchedBlocks,
                resources = resources,
                combo = combo,
                blockType = blockType
            };

            EventSystem.Publish(matchEvent);

            // Apply gravity
            StartCoroutine(ApplyGravityCoroutine());
        }

        private int CalculateCombo(int matchCount)
        {
            if (matchCount >= 6) return 4;
            if (matchCount >= 5) return 3;
            if (matchCount >= 4) return 2;
            return 1;
        }

        private void RemoveWasteBlock(Vector2Int position)
        {
            var wasteBlock = GetWasteBlock(position);
            if (wasteBlock != null)
            {
                // Play destruction effect
                PlayDestructionEffect(position, wasteBlock.blockType);
                
                // Remove from grid
                SetWasteBlock(position, null);
                
                // Destroy game object
                if (wasteBlock.gameObject != null)
                {
                    Destroy(wasteBlock.gameObject);
                }
            }
        }

        private void PlayDestructionEffect(Vector2Int position, WasteBlockType blockType)
        {
            Vector3 worldPos = GridToWorldPosition(position);
            
            // Create particle effect
            // This would be implemented with a particle system
            Debug.Log($"Playing destruction effect for {blockType} at {position}");
        }

        private IEnumerator ApplyGravityCoroutine()
        {
            yield return new WaitForSeconds(0.2f);
            
            bool blocksMoving = true;
            while (blocksMoving)
            {
                blocksMoving = false;
                
                for (int x = 0; x < gridWidth; x++)
                {
                    for (int y = 1; y < gridHeight; y++)
                    {
                        if (grid[x, y] != null && grid[x, y - 1] == null)
                        {
                            // Move block down
                            grid[x, y - 1] = grid[x, y];
                            grid[x, y] = null;
                            
                            // Update visual position
                            Vector3 newPos = GridToWorldPosition(new Vector2Int(x, y - 1));
                            StartCoroutine(MoveBlockToPosition(grid[x, y - 1].gameObject, newPos));
                            
                            blocksMoving = true;
                        }
                    }
                }
                
                yield return new WaitForSeconds(0.1f);
            }
        }

        private IEnumerator MoveBlockToPosition(GameObject block, Vector3 targetPosition)
        {
            Vector3 startPosition = block.transform.position;
            float duration = 0.3f;
            float elapsed = 0f;

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / duration;
                block.transform.position = Vector3.Lerp(startPosition, targetPosition, t);
                yield return null;
            }

            block.transform.position = targetPosition;
        }

        public bool TryBuildTower(Vector2Int position)
        {
            if (!IsValidPosition(position) || GetWasteBlock(position) != null)
                return false;

            // Check if position is blocked
            GameObject cellObj = cellObjects[position.x, position.y];
            if (cellObj != null)
            {
                GridCell cell = cellObj.GetComponent<GridCell>();
                if (cell != null && cell.IsBlocked)
                    return false;
            }

            // For now, just return true - tower building will be handled by TowerSystem
            return true;
        }

        public void ClearGrid()
        {
            if (grid != null)
            {
                for (int x = 0; x < gridWidth; x++)
                {
                    for (int y = 0; y < gridHeight; y++)
                    {
                        if (grid[x, y] != null && grid[x, y].gameObject != null)
                        {
                            DestroyImmediate(grid[x, y].gameObject);
                        }
                        
                        if (cellObjects != null && cellObjects[x, y] != null)
                        {
                            DestroyImmediate(cellObjects[x, y]);
                        }
                    }
                }
            }

            if (purifierObject != null)
            {
                DestroyImmediate(purifierObject);
            }
        }

        public List<Vector2Int> GetEmptyPositions()
        {
            List<Vector2Int> emptyPositions = new List<Vector2Int>();
            
            for (int x = 0; x < gridWidth; x++)
            {
                for (int y = 0; y < gridHeight; y++)
                {
                    if (grid[x, y] == null)
                    {
                        emptyPositions.Add(new Vector2Int(x, y));
                    }
                }
            }
            
            return emptyPositions;
        }

        public List<Vector2Int> GetOccupiedPositions()
        {
            List<Vector2Int> occupiedPositions = new List<Vector2Int>();
            
            for (int x = 0; x < gridWidth; x++)
            {
                for (int y = 0; y < gridHeight; y++)
                {
                    if (grid[x, y] != null)
                    {
                        occupiedPositions.Add(new Vector2Int(x, y));
                    }
                }
            }
            
            return occupiedPositions;
        }

        public Vector2Int GetPurifierPosition()
        {
            return purifierPosition;
        }

        private void OnDrawGizmos()
        {
            // Draw grid in editor
            Gizmos.color = Color.white;
            
            for (int x = 0; x <= gridWidth; x++)
            {
                Vector3 start = gridOrigin + new Vector3(x * cellSize, 0, 0);
                Vector3 end = gridOrigin + new Vector3(x * cellSize, gridHeight * cellSize, 0);
                Gizmos.DrawLine(start, end);
            }
            
            for (int y = 0; y <= gridHeight; y++)
            {
                Vector3 start = gridOrigin + new Vector3(0, y * cellSize, 0);
                Vector3 end = gridOrigin + new Vector3(gridWidth * cellSize, y * cellSize, 0);
                Gizmos.DrawLine(start, end);
            }
        }
    }
}
